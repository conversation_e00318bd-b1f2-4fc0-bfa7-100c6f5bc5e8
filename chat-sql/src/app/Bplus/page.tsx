'use client';

import React, { useState, useCallback } from 'react';
import { Box, Snackbar, Alert } from '@mui/material';
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import BPlusTreeVisualizer, { TreeState, BPlusTreeOperations } from '@/components/BPlusXyflow/BPlusTreeVisualizer';
import BPlusOperationPanel from '@/components/BPlusXyflow/BPlusOperationPanel';
import HistoryManagementPanel from '@/components/BPlusHistory/HistoryManagementPanel';
import ChatReservedArea from '@/components/BPlusHistory/ChatReservedArea';
import BPlusSidebar from '@/components/BPlusHistory/BPlusSidebar';
import NewSessionModal, { NewSessionFormData } from '@/components/BPlusHistory/NewSessionModal';
import ClearAllConfirmDialog from '@/components/BPlusHistory/ClearAllConfirmDialog';
import { HistorySession, HistoryStep } from '@/types/bPlusHistory';
import '@/styles/globalSidebar.css';

/**
 * B+树操作历史页面 - 支持版本控制与回溯功能
 * 采用左右布局：左侧历史管理面板，右侧分为上下两部分（B+树渲染区域和操作控制区域）
 */
const BPlusHistoryPage: React.FC = () => {
  // B+树状态管理
  const [currentTreeState, setCurrentTreeState] = useState<TreeState | null>(null);
  const [order, setOrder] = useState<number>(3);

  // 历史管理状态
  const [selectedSessionId, setSelectedSessionId] = useState<string>();
  const [selectedStepIndex, setSelectedStepIndex] = useState<number>();
  const [showHistory, setShowHistory] = useState<boolean>(true);
  const [historySteps, setHistorySteps] = useState<HistoryStep[]>([]);
  const [currentSession, setCurrentSession] = useState<HistorySession | null>(null);

  // 新建会话模态框状态
  const [isNewSessionModalOpen, setIsNewSessionModalOpen] = useState<boolean>(false);
  const [isCreatingSession, setIsCreatingSession] = useState<boolean>(false);

  // 清理确认对话框状态
  const [isClearAllDialogOpen, setIsClearAllDialogOpen] = useState<boolean>(false);
  const [isClearingAll, setIsClearingAll] = useState<boolean>(false);

  // B+树操作接口
  const [treeOperations, setTreeOperations] = useState<BPlusTreeOperations | null>(null);

  // 操作面板设置状态
  const [operationSettings, setOperationSettings] = useState({
    isAnimationEnabled: true,
    animationSpeed: 500,
    order: order
  });

  // 消息状态
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'info' | 'warning' | 'error';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // 消息处理函数
  const showMessage = useCallback((message: string, severity: 'success' | 'info' | 'warning' | 'error' = 'info') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  }, []);

  // B+树状态变更处理
  const handleTreeStateChange = useCallback((state: TreeState) => {
    setCurrentTreeState(state);

    // 创建新的历史步骤
    const newStep: HistoryStep = {
      id: `step-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      operation: state.operation || 'initial',
      key: state.operationKey,
      timestamp: state.timestamp,
      nodes: [...state.nodes],
      edges: [...state.edges],
      keys: [...state.keys],
      description: getOperationDescription(state.operation, state.operationKey),
      success: true
    };

    // 添加到历史步骤
    setHistorySteps(prev => [...prev, newStep]);

    // 如果有当前会话，更新会话的步骤
    if (currentSession) {
      const updatedSession: HistorySession = {
        ...currentSession,
        steps: [...currentSession.steps, newStep],
        currentStepIndex: currentSession.steps.length,
        updatedAt: Date.now()
      };
      setCurrentSession(updatedSession);
    }
  }, [currentSession]);

  // 获取操作描述
  const getOperationDescription = (operation?: string, key?: number): string => {
    switch (operation) {
      case 'insert':
        return `插入键值 ${key}`;
      case 'delete':
        return `删除键值 ${key}`;
      case 'reset':
        return '重置B+树';
      case 'initial':
        return '初始化B+树';
      default:
        return '未知操作';
    }
  };

  // 历史管理回调函数
  const handleSessionSelect = useCallback((sessionId: string) => {
    setSelectedSessionId(sessionId);
    setSelectedStepIndex(undefined);

    // 这里应该从存储中加载会话数据，目前使用模拟数据
    // TODO: 集成真实的历史存储服务
    const mockSession: HistorySession = {
      id: sessionId,
      name: `会话 ${sessionId}`,
      order: order,
      steps: historySteps,
      currentStepIndex: historySteps.length - 1,
      createdAt: Date.now() - 3600000,
      updatedAt: Date.now(),
      description: '模拟会话数据',
      tags: ['模拟'],
      isCompleted: false,
      statistics: {
        totalOperations: historySteps.length,
        insertCount: historySteps.filter(s => s.operation === 'insert').length,
        deleteCount: historySteps.filter(s => s.operation === 'delete').length,
        resetCount: historySteps.filter(s => s.operation === 'reset').length,
        successCount: historySteps.filter(s => s.success).length,
        errorCount: historySteps.filter(s => !s.success).length,
        totalDuration: 0
      }
    };

    setCurrentSession(mockSession);
    showMessage(`已选择会话: ${mockSession.name}`, 'info');
  }, [showMessage, order, historySteps]);

  const handleStepSelect = useCallback((stepIndex: number) => {
    setSelectedStepIndex(stepIndex);

    // 历史回溯：根据选中的步骤更新B+树状态
    if (currentSession && currentSession.steps[stepIndex]) {
      const selectedStep = currentSession.steps[stepIndex];
      const newTreeState: TreeState = {
        nodes: [...selectedStep.nodes],
        edges: [...selectedStep.edges],
        keys: [...selectedStep.keys],
        operation: selectedStep.operation,
        operationKey: selectedStep.key,
        timestamp: selectedStep.timestamp
      };
      setCurrentTreeState(newTreeState);
      showMessage(`已回溯到步骤: ${stepIndex + 1} - ${selectedStep.description}`, 'info');
    } else {
      showMessage(`已选择步骤: ${stepIndex + 1}`, 'info');
    }
  }, [showMessage, currentSession]);

  // 打开新建会话模态框
  const handleOpenNewSessionModal = useCallback(() => {
    setIsNewSessionModalOpen(true);
  }, []);

  // 关闭新建会话模态框
  const handleCloseNewSessionModal = useCallback(() => {
    setIsNewSessionModalOpen(false);
    setIsCreatingSession(false);
  }, []);

  // 创建新会话
  const handleCreateSession = useCallback(async (formData: NewSessionFormData) => {
    setIsCreatingSession(true);

    try {
      // 创建新的历史会话
      const newSession: HistorySession = {
        id: `session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        name: formData.name,
        order: formData.order,
        steps: [],
        currentStepIndex: -1,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        description: formData.description || '新建的B+树操作会话',
        tags: formData.tags || ['新建'],
        isCompleted: false,
        statistics: {
          totalOperations: 0,
          insertCount: 0,
          deleteCount: 0,
          resetCount: 0,
          successCount: 0,
          errorCount: 0,
          totalDuration: 0
        }
      };

      setCurrentSession(newSession);
      setSelectedSessionId(newSession.id);
      setHistorySteps([]);
      setCurrentTreeState(null); // 重置树状态
      setOrder(formData.order); // 设置新的阶数

      // 关闭模态框
      setIsNewSessionModalOpen(false);
      showMessage(`已创建新会话: ${newSession.name}`, 'success');
    } catch (error) {
      console.error('创建会话失败:', error);
      showMessage('创建会话失败，请重试', 'error');
    } finally {
      setIsCreatingSession(false);
    }
  }, [showMessage]);

  const handleDeleteSession = useCallback((sessionId: string) => {
    showMessage(`删除会话 ${sessionId} 功能开发中...`, 'warning');
  }, [showMessage]);

  const handleRenameSession = useCallback((sessionId: string, newName: string) => {
    showMessage(`重命名会话 ${sessionId} 为 ${newName} 功能开发中...`, 'info');
  }, [showMessage]);

  // 打开清理确认对话框
  const handleOpenClearAllDialog = useCallback(() => {
    setIsClearAllDialogOpen(true);
  }, []);

  // 关闭清理确认对话框
  const handleCloseClearAllDialog = useCallback(() => {
    setIsClearAllDialogOpen(false);
    setIsClearingAll(false);
  }, []);

  // 确认清理所有会话
  const handleDeleteAllSessions = useCallback(async () => {
    setIsClearingAll(true);

    try {
      // 清理所有会话数据
      setCurrentSession(null);
      setSelectedSessionId(undefined);
      setSelectedStepIndex(undefined);
      setHistorySteps([]);
      setCurrentTreeState(null);

      // TODO: 集成真实的存储服务清理
      // await historyStorageService.clearAllSessions();

      // 关闭对话框
      setIsClearAllDialogOpen(false);
      showMessage('已清理所有历史记录', 'success');
    } catch (error) {
      console.error('清理历史记录失败:', error);
      showMessage('清理历史记录失败，请重试', 'error');
    } finally {
      setIsClearingAll(false);
    }
  }, [showMessage]);

  // 处理B+树操作接口就绪
  const handleOperationsReady = useCallback((operations: BPlusTreeOperations) => {
    setTreeOperations(operations);
  }, []);

  // 操作面板设置变更处理
  const handleOperationSettingsChange = useCallback((newSettings: typeof operationSettings) => {
    setOperationSettings(newSettings);
    // 如果阶数变更，同时更新主状态
    if (newSettings.order !== order) {
      setOrder(newSettings.order);
    }
  }, [order]);

  // 操作面板的操作处理函数
  const handleOperationInsert = useCallback(async (value: number) => {
    if (treeOperations) {
      await treeOperations.insert(value);
    }
  }, [treeOperations]);

  const handleOperationDelete = useCallback(async (value: number) => {
    if (treeOperations) {
      await treeOperations.delete(value);
    }
  }, [treeOperations]);

  const handleOperationReset = useCallback(() => {
    if (treeOperations) {
      treeOperations.reset();
    }
  }, [treeOperations]);

  const handleOperationSave = useCallback(async () => {
    if (treeOperations) {
      await treeOperations.save();
    }
  }, [treeOperations]);

  const handleOperationRestore = useCallback(async () => {
    if (treeOperations) {
      await treeOperations.restore();
    }
  }, [treeOperations]);

  // 对话框回调函数
  const handleSendMessage = useCallback((message: string) => {
    showMessage(`发送消息: ${message}`, 'info');
  }, [showMessage]);

  const handleGetHelp = useCallback(() => {
    showMessage('获取帮助功能开发中...', 'info');
  }, [showMessage]);

  const handleGetSuggestion = useCallback(() => {
    showMessage('获取建议功能开发中...', 'info');
  }, [showMessage]);

  // 历史记录切换处理
  const handleToggleHistory = useCallback(() => {
    setShowHistory(prev => !prev);
    showMessage(!showHistory ? '已显示历史记录区域' : '已隐藏历史记录区域', 'info');
  }, [showHistory, showMessage]);

  // Snackbar关闭处理
  const handleSnackbarClose = useCallback(() => {
    setSnackbar(prev => ({ ...prev, open: false }));
  }, []);

  return (
    <Box
      className = "pageContainer"
      sx={{
      width: '100vw',
      height: '100vh',
      display: 'flex',
      bgcolor: 'var(--background-color)',
      overflow: 'hidden',
    }}>

      {/* 侧边栏 */}
      <Box sx={{ flexShrink: 0 }}>
        <BPlusSidebar
          showHistory={showHistory}
          onToggleHistory={handleToggleHistory}
          onNewRecord={handleOpenNewSessionModal}
        />
      </Box>

      {/* 可拖拽的面板组实现两栏布局 */}
      <PanelGroup direction="horizontal" style={{ height: "100vh", flex: 1 }}>

        {/* 左侧：历史管理面板（条件显示） */}
        {showHistory && (
          <>
            <Panel minSize={15} maxSize={40} defaultSize={20}>
          <Box sx={{
            height: "100%",
            p: 0,
            bgcolor: 'var(--background-color)',
            borderRight: '1px solid var(--card-border)',
            overflow: 'hidden'
          }}>
            <HistoryManagementPanel
              selectedSessionId={selectedSessionId}
              selectedStepIndex={selectedStepIndex}
              onSessionSelect={handleSessionSelect}
              onStepSelect={handleStepSelect}
              onCreateSession={handleOpenNewSessionModal}
              onDeleteSession={handleDeleteSession}
              onRenameSession={handleRenameSession}
              onDeleteAllSessions={handleOpenClearAllDialog}
            />
          </Box>
        </Panel>

        {/* 拖拽手柄 */}
        <PanelResizeHandle style={{
          width: 6,
          background: "var(--card-border)",
          cursor: "col-resize",
          transition: "background-color 0.2s ease"
        }} />
        </>
        )}

        {/* 右侧：B+树可视化和操作区域 */}
        <Panel minSize={50} defaultSize={75}>
          <PanelGroup direction="vertical" style={{ height: "100%" }}>
            
            {/* 上部分：B+树渲染区域 */}
            <Panel minSize={40} defaultSize={65}>
              <Box sx={{ 
                height: "100%", 
                position: "relative",
                bgcolor: 'var(--background-color)'
              }}>
                {/* 受控的BPlusTreeVisualizer */}
                <BPlusTreeVisualizer
                  initialKeys={currentTreeState ? [] : [1, 2, 3, 4, 5]}
                  order={order}
                  externalNodes={currentTreeState?.nodes}
                  externalEdges={currentTreeState?.edges}
                  onStateChange={handleTreeStateChange}
                  onOperationsReady={handleOperationsReady}
                />
              </Box>
            </Panel>

            {/* 拖拽手柄 */}
            <PanelResizeHandle style={{ 
              height: 6, 
              background: "var(--card-border)", 
              cursor: "row-resize",
              transition: "background-color 0.2s ease"
            }} />

            {/* 下部分：操作面板和对话区域 */}
            <Panel minSize={20} defaultSize={35}>
              <PanelGroup direction="horizontal" style={{ height: "100%" }}>

                {/* 左侧：操作面板 */}
                <Panel minSize={25} defaultSize={40}>
                  <Box sx={{
                    height: "100%",
                    p: 2,
                    bgcolor: 'var(--background-color)',
                    borderTop: '1px solid var(--card-border)',
                    overflow: 'hidden'
                  }}>
                    <BPlusOperationPanel
                      settings={operationSettings}
                      onSettingsChange={handleOperationSettingsChange}
                      isAnimating={false} // TODO: 从BPlusTreeVisualizer获取动画状态
                      onInsert={handleOperationInsert}
                      onDelete={handleOperationDelete}
                      onReset={handleOperationReset}
                      onSave={handleOperationSave}
                      onRestore={handleOperationRestore}
                      showMessage={showMessage}
                    />
                  </Box>
                </Panel>

                {/* 拖拽手柄 */}
                <PanelResizeHandle style={{
                  width: 6,
                  background: "var(--card-border)",
                  cursor: "col-resize",
                  transition: "background-color 0.2s ease"
                }} />

                {/* 右侧：智能助手对话区域 */}
                <Panel minSize={30} defaultSize={60}>
                  <Box sx={{
                    height: "100%",
                    p: 2,
                    bgcolor: 'var(--background-color)',
                    borderTop: '1px solid var(--card-border)',
                    borderLeft: '1px solid var(--card-border)',
                    overflow: 'hidden'
                  }}>
                    <ChatReservedArea
                      onSendMessage={handleSendMessage}
                      onGetHelp={handleGetHelp}
                      onGetSuggestion={handleGetSuggestion}
                    />
                  </Box>
                </Panel>

              </PanelGroup>
            </Panel>

          </PanelGroup>
        </Panel>

      </PanelGroup>

      {/* 新建会话模态框 */}
      <NewSessionModal
        open={isNewSessionModalOpen}
        onClose={handleCloseNewSessionModal}
        onConfirm={handleCreateSession}
        loading={isCreatingSession}
      />

      {/* 清理所有记录确认对话框 */}
      <ClearAllConfirmDialog
        open={isClearAllDialogOpen}
        onClose={handleCloseClearAllDialog}
        onConfirm={handleDeleteAllSessions}
        loading={isClearingAll}
        sessionCount={0} // TODO: 传递真实的会话数量
      />

      {/* Snackbar 消息提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        onClose={handleSnackbarClose}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default BPlusHistoryPage;
